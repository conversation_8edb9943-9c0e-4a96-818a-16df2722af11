-- NPCManager.lua (Script)
-- Manages all NPCs in the game

local NPCManager = {}

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local Players = game:GetService("Players")

-- Import dependencies
local BaseNPC = require(script.Parent.BaseNPC)
local CommandHandler = require(script.Parent.CommandHandler)
local DataService = require(script.Parent.Parent.Services.DataService)
local NPCConfigs = require(ReplicatedStorage.NPCSystem.Data.NPCConfigs)

-- Remote events
local NPCRemotes = ReplicatedStorage.NPCRemotes
local PurchaseNPC = NPCRemotes.PurchaseNPC
local SendCommand = NPCRemotes.SendCommand
local NPCStatusUpdate = NPCRemotes.NPCStatusUpdate
local GetNPCList = NPCRemotes.GetNPCList

-- Storage
NPCManager.activeNPCs = {}
NPCManager.playerNPCs = {}

-- Initialize the manager
function NPCManager:init()
    print("NPCManager initializing...")
    
    -- Connect remote events
    PurchaseNPC.OnServerEvent:Connect(function(player, npcType)
        self:handleNPCPurchase(player, npcType)
    end)
    
    SendCommand.OnServerEvent:Connect(function(player, npcId, command, ...)
        self:handleCommand(player, npcId, command, ...)
    end)
    
    GetNPCList.OnServerInvoke = function(player)
        return self:getPlayerNPCs(player)
    end
    
    -- Start update loop
    RunService.Heartbeat:Connect(function(deltaTime)
        self:update(deltaTime)
    end)
    
    print("NPCManager initialized successfully")
end

function NPCManager:handleNPCPurchase(player, npcType)
    print("Purchase request from", player.Name, "for", npcType)
    
    -- Check if NPC type exists
    local config = NPCConfigs[npcType]
    if not config then
        warn("Invalid NPC type:", npcType)
        return
    end
    
    -- Check player's currency (implement your currency system here)
    local playerData = DataService:getPlayerData(player)
    if not playerData or playerData.currency < config.cost then
        print("Player", player.Name, "doesn't have enough currency")
        return
    end
    
    -- Create the NPC
    local npc = self:createNPC(npcType, config, player)
    if npc then
        -- Deduct currency
        DataService:updatePlayerCurrency(player, -config.cost)
        
        -- Add to player's NPCs
        if not self.playerNPCs[player] then
            self.playerNPCs[player] = {}
        end
        table.insert(self.playerNPCs[player], npc)
        
        -- Notify client
        NPCStatusUpdate:FireClient(player, "purchased", {
            npcId = npc.id,
            npcType = npcType,
            name = npc.name
        })
        
        print("NPC", npc.name, "purchased by", player.Name)
    end
end

function NPCManager:createNPC(npcType, config, owner)
    -- Create NPC model (you'll need to implement model creation based on your assets)
    local npcModel = self:createNPCModel(npcType, config)
    if not npcModel then
        warn("Failed to create NPC model for type:", npcType)
        return nil
    end
    
    -- Create NPC instance
    local npc = BaseNPC.new(npcModel, config)
    npc:setOwner(owner)
    
    -- Add to active NPCs
    self.activeNPCs[npc.id] = npc
    
    return npc
end

function NPCManager:createNPCModel(npcType, config)
    -- This is a placeholder - implement based on your NPC models
    -- You might load from ReplicatedStorage or create procedurally
    
    local model = Instance.new("Model")
    model.Name = config.name or npcType
    
    -- Create basic humanoid structure
    local humanoid = Instance.new("Humanoid")
    humanoid.Parent = model
    
    -- Add basic parts (you'll want to replace this with actual NPC models)
    local head = Instance.new("Part")
    head.Name = "Head"
    head.Size = Vector3.new(2, 1, 1)
    head.BrickColor = BrickColor.new("Bright yellow")
    head.Parent = model
    
    -- Create both Torso (R6) and HumanoidRootPart (R15) for compatibility
    local torso = Instance.new("Part")
    torso.Name = "Torso"
    torso.Size = Vector3.new(2, 2, 1)
    torso.BrickColor = BrickColor.new("Bright blue")
    torso.Parent = model

    local humanoidRootPart = Instance.new("Part")
    humanoidRootPart.Name = "HumanoidRootPart"
    humanoidRootPart.Size = Vector3.new(2, 2, 1)
    humanoidRootPart.BrickColor = BrickColor.new("Bright blue")
    humanoidRootPart.Transparency = 1 -- Make it invisible
    humanoidRootPart.CanCollide = false
    humanoidRootPart.Parent = model

    -- Set PrimaryPart for the model
    model.PrimaryPart = humanoidRootPart
    
    -- Position in workspace
    model.Parent = workspace
    model:MoveTo(Vector3.new(0, 5, 0))
    
    return model
end

function NPCManager:handleCommand(player, npcId, command, ...)
    local npc = self.activeNPCs[npcId]
    if not npc then
        warn("NPC not found:", npcId)
        return
    end
    
    -- Check if player owns this NPC
    if npc.owner ~= player then
        warn("Player", player.Name, "doesn't own NPC", npcId)
        return
    end
    
    -- Execute command through command handler
    local success = CommandHandler:executeCommand(npc, command, ...)
    
    if success then
        NPCStatusUpdate:FireClient(player, "command_executed", {
            npcId = npcId,
            command = command
        })
    end
end

function NPCManager:getPlayerNPCs(player)
    local npcs = self.playerNPCs[player] or {}
    local npcList = {}
    
    for _, npc in ipairs(npcs) do
        if npc.isActive then
            table.insert(npcList, {
                id = npc.id,
                name = npc.name,
                type = npc.config.type or "unknown"
            })
        end
    end
    
    return npcList
end

function NPCManager:update(deltaTime)
    for _, npc in pairs(self.activeNPCs) do
        if npc.isActive then
            npc:update(deltaTime)
        end
    end
end

function NPCManager:removeNPC(npcId)
    local npc = self.activeNPCs[npcId]
    if npc then
        npc:destroy()
        self.activeNPCs[npcId] = nil
        
        -- Remove from player's NPC list
        if npc.owner and self.playerNPCs[npc.owner] then
            for i, playerNPC in ipairs(self.playerNPCs[npc.owner]) do
                if playerNPC.id == npcId then
                    table.remove(self.playerNPCs[npc.owner], i)
                    break
                end
            end
        end
    end
end

-- Handle player leaving
Players.PlayerRemoving:Connect(function(player)
    if NPCManager.playerNPCs[player] then
        for _, npc in ipairs(NPCManager.playerNPCs[player]) do
            NPCManager:removeNPC(npc.id)
        end
        NPCManager.playerNPCs[player] = nil
    end
end)

-- Initialize when script runs
NPCManager:init()

return NPCManager
