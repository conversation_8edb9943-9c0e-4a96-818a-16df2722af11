-- RemoteSetup.lua (Script)
-- Automatically sets up required RemoteEvents and RemoteFunction for the NPC system

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Function to create or get a folder
local function getOrCreateFolder(parent, name)
    local folder = parent:FindFirstChild(name)
    if not folder then
        folder = Instance.new("Folder")
        folder.Name = name
        folder.Parent = parent
        print("Created folder:", name)
    end
    return folder
end

-- Function to create or get a RemoteEvent
local function getOrCreateRemoteEvent(parent, name)
    local remoteEvent = parent:FindFirstChild(name)
    if not remoteEvent then
        remoteEvent = Instance.new("RemoteEvent")
        remoteEvent.Name = name
        remoteEvent.Parent = parent
        print("Created RemoteEvent:", name)
    elseif not remoteEvent:IsA("RemoteEvent") then
        -- If it exists but is not a RemoteEvent (like a .lua file), replace it
        remoteEvent:Destroy()
        remoteEvent = Instance.new("RemoteEvent")
        remoteEvent.Name = name
        remoteEvent.Parent = parent
        print("Replaced with RemoteEvent:", name)
    end
    return remoteEvent
end

-- Function to create or get a RemoteFunction
local function getOrCreateRemoteFunction(parent, name)
    local remoteFunction = parent:FindFirstChild(name)
    if not remoteFunction then
        remoteFunction = Instance.new("RemoteFunction")
        remoteFunction.Name = name
        remoteFunction.Parent = parent
        print("Created RemoteFunction:", name)
    elseif not remoteFunction:IsA("RemoteFunction") then
        -- If it exists but is not a RemoteFunction (like a .lua file), replace it
        remoteFunction:Destroy()
        remoteFunction = Instance.new("RemoteFunction")
        remoteFunction.Name = name
        remoteFunction.Parent = parent
        print("Replaced with RemoteFunction:", name)
    end
    return remoteFunction
end

-- Main setup function
local function setupRemotes()
    print("Setting up NPC System RemoteEvents...")
    
    -- Create NPCRemotes folder
    local npcRemotesFolder = getOrCreateFolder(ReplicatedStorage, "NPCRemotes")
    
    -- Create required RemoteEvents
    getOrCreateRemoteEvent(npcRemotesFolder, "PurchaseNPC")
    getOrCreateRemoteEvent(npcRemotesFolder, "SendCommand")
    getOrCreateRemoteEvent(npcRemotesFolder, "NPCStatusUpdate")
    
    -- Create required RemoteFunction
    getOrCreateRemoteFunction(npcRemotesFolder, "GetNPCList")
    
    print("NPC System RemoteEvents setup complete!")
end

-- Run setup
setupRemotes()
