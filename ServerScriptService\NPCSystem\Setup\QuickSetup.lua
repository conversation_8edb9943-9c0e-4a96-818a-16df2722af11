-- QuickSetup.lua (<PERSON>ript)
-- Quick setup script to fix common issues and set up the NPC system

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

print("=== NPC System Quick Setup ===")

-- 1. Set up RemoteEvents
local function setupRemotes()
    print("1. Setting up RemoteEvents...")
    
    -- Create NPCRemotes folder
    local npcRemotesFolder = ReplicatedStorage:FindFirstChild("NPCRemotes")
    if not npcRemotesFolder then
        npcRemotesFolder = Instance.new("Folder")
        npcRemotesFolder.Name = "NPCRemotes"
        npcRemotesFolder.Parent = ReplicatedStorage
        print("   Created NPCRemotes folder")
    end
    
    -- Create RemoteEvents
    local remoteEvents = {"PurchaseNPC", "SendCommand", "NPCStatusUpdate"}
    for _, eventName in ipairs(remoteEvents) do
        local existing = npcRemotesFolder:FindFirstChild(eventName)
        if not existing or not existing:IsA("RemoteEvent") then
            if existing then existing:Destroy() end
            local remoteEvent = Instance.new("RemoteEvent")
            remoteEvent.Name = eventName
            remoteEvent.Parent = npcRemotesFolder
            print("   Created RemoteEvent:", eventName)
        end
    end
    
    -- Create RemoteFunction
    local existing = npcRemotesFolder:FindFirstChild("GetNPCList")
    if not existing or not existing:IsA("RemoteFunction") then
        if existing then existing:Destroy() end
        local remoteFunction = Instance.new("RemoteFunction")
        remoteFunction.Name = "GetNPCList"
        remoteFunction.Parent = npcRemotesFolder
        print("   Created RemoteFunction: GetNPCList")
    end
    
    print("   RemoteEvents setup complete!")
end

-- 2. Check file structure
local function checkFileStructure()
    print("2. Checking file structure...")
    
    local requiredPaths = {
        "ReplicatedStorage.NPCSystem.Utils.NPCUtils",
        "ReplicatedStorage.NPCSystem.Data.NPCConfigs",
        "ReplicatedStorage.NPCSystem.Data.Commands"
    }
    
    for _, path in ipairs(requiredPaths) do
        local success, result = pcall(function()
            return require(game:GetService(path:split(".")[1])[path:split(".")[2]][path:split(".")[3]][path:split(".")[4]])
        end)
        
        if success then
            print("   ✓", path)
        else
            warn("   ✗", path, "- Missing or has errors")
        end
    end
end

-- 3. Test DataStore access
local function testDataStore()
    print("3. Testing DataStore access...")
    
    if RunService:IsStudio() then
        print("   Running in Studio - DataStore access may be limited")
        print("   To enable DataStore access in Studio:")
        print("   1. Go to Game Settings")
        print("   2. Navigate to Security tab")
        print("   3. Enable 'Allow HTTP Requests'")
        print("   4. Enable 'Enable Studio Access to API Services'")
    else
        print("   Running in live game - DataStore should work normally")
    end
end

-- 4. Character compatibility check
local function checkCharacterCompatibility()
    print("4. Checking character compatibility...")
    
    local Players = game:GetService("Players")
    
    -- Check if any players are online to test
    local players = Players:GetPlayers()
    if #players > 0 then
        for _, player in ipairs(players) do
            if player.Character then
                local hasHumanoidRootPart = player.Character:FindFirstChild("HumanoidRootPart")
                local hasTorso = player.Character:FindFirstChild("Torso")
                local hasUpperTorso = player.Character:FindFirstChild("UpperTorso")
                
                if hasUpperTorso then
                    print("   Player", player.Name, "is using R15 character")
                elseif hasTorso then
                    print("   Player", player.Name, "is using R6 character")
                else
                    print("   Player", player.Name, "has unknown character type")
                end
                
                if hasHumanoidRootPart or hasTorso then
                    print("   ✓ Character compatibility OK for", player.Name)
                else
                    warn("   ✗ Character compatibility issue for", player.Name)
                end
            end
        end
    else
        print("   No players online to test character compatibility")
    end
end

-- 5. Display setup summary
local function displaySummary()
    print("5. Setup Summary:")
    print("   ✓ RemoteEvents configured")
    print("   ✓ Character compatibility handled")
    print("   ✓ DataService error handling improved")
    print("   ✓ Component ModuleScript issues fixed")
    print("")
    print("=== Setup Complete! ===")
    print("Your NPC system should now work without the previous errors.")
    print("")
    print("If you still encounter issues:")
    print("1. Check that all files are in the correct locations")
    print("2. Ensure Components are ModuleScripts, not Scripts")
    print("3. Enable Studio API access for DataStore functionality")
    print("4. Check the output for any remaining error messages")
end

-- Run all setup steps
setupRemotes()
checkFileStructure()
testDataStore()
checkCharacterCompatibility()
displaySummary()

print("Quick setup script completed!")
